import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  TrendingUp, 
  TrendingDown,
  Plus, 
  Building2, 
  DollarSign, 
  Users, 
  Calendar,
  Target,
  Award,
  Eye,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  PieChart,
  Zap,
  Star,
  Filter,
  Search,
  Bell,
  Settings
} from "lucide-react";
import { useListings } from "@/hooks/useQueryApi";

const Dashboard2: React.FC = () => {
  const { listings, loading, error } = useListings({ 
    limit: 12, 
    sortBy: 'created_at', 
    sortOrder: 'asc' 
  });

  const kpiData = React.useMemo(() => {
    if (loading || !listings.length) {
      return {
        activeListings: 0,
        underContract: 0,
        closedThisMonth: 0,
        pipelineCommissions: 0,
        totalRevenue: 0,
        avgDeal: 0,
        conversionRate: 0,
        avgDaysToClose: 0,
      };
    }

    const activeListings = listings.filter(l => l.status === 'Active').length;
    const underContract = listings.filter(l => l.status === 'Under Contract').length;
    const closedThisMonth = listings.filter(l => l.status === 'Sold').length;
    
    const totalRevenue = listings
      .filter(l => l.status === 'Sold')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0), 0);
      
    const pipelineCommissions = listings
      .filter(l => l.status === 'Active' || l.status === 'Under Contract')
      .reduce((sum, listing) => sum + (listing.askingPrice || 0) * 0.03, 0);

    const avgDeal = closedThisMonth > 0 ? totalRevenue / closedThisMonth : 0;
    const conversionRate = activeListings > 0 ? (closedThisMonth / activeListings) * 100 : 0;
    const avgDaysToClose = 45; // Mock data

    return {
      activeListings,
      underContract,
      closedThisMonth,
      pipelineCommissions,
      totalRevenue,
      avgDeal,
      conversionRate,
      avgDaysToClose,
    };
  }, [listings, loading]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-emerald-100 text-emerald-800 border-emerald-200';
      case 'Under Contract': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'Sold': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-slate-100 text-slate-800 border-slate-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-slate-50">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-4 border-primary border-t-transparent"></div>
          <p className="text-muted-foreground">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-50/50">
      {/* Modern Header */}
      <div className="bg-white border-b border-slate-200/60 sticky top-0 z-10 backdrop-blur-sm bg-white/95">
        <div className="px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-primary to-primary/80 rounded-xl flex items-center justify-center">
                  <BarChart3 className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-slate-900">Dashboard</h1>
                  <p className="text-sm text-slate-600">Welcome back! Here's your business overview</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button variant="outline" size="sm" className="hidden sm:flex">
                <Filter className="w-4 h-4 mr-2" />
                Filter
              </Button>
              <Button variant="outline" size="sm" className="hidden sm:flex">
                <Bell className="w-4 h-4" />
              </Button>
              <Button className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80" asChild>
                <Link to="/listings/new">
                  <Plus className="w-4 h-4 mr-2" />
                  New Listing
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        {/* Enhanced KPI Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Active Listings */}
          <Card className="relative overflow-hidden border-0 shadow-sm bg-white hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Active Listings</p>
                  <p className="text-3xl font-bold text-slate-900">{kpiData.activeListings}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-emerald-500" />
                    <span className="text-emerald-600 font-medium">+12%</span>
                    <span className="text-slate-500">vs last month</span>
                  </div>
                </div>
             
              </div>
            </CardContent>
          </Card>

          {/* Under Contract */}
          <Card className="relative overflow-hidden border-0 shadow-sm bg-white hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Under Contract</p>
                  <p className="text-3xl font-bold text-slate-900">{kpiData.underContract}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <Clock className="w-4 h-4 text-amber-500" />
                    <span className="text-amber-600 font-medium">Avg {kpiData.avgDaysToClose} days</span>
                  </div>
                </div>
               
              </div>
            </CardContent>
          </Card>

          {/* Closed This Month */}
          <Card className="relative overflow-hidden border-0 shadow-sm bg-white hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Closed This Month</p>
                  <p className="text-3xl font-bold text-slate-900">{kpiData.closedThisMonth}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <Target className="w-4 h-4 text-blue-500" />
                    <span className="text-blue-600 font-medium">{kpiData.conversionRate.toFixed(1)}%</span>
                    <span className="text-slate-500">conversion</span>
                  </div>
                </div>
            
              </div>
            </CardContent>
          </Card>

          {/* Total Revenue */}
          <Card className="relative overflow-hidden border-0 shadow-sm bg-white hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <p className="text-sm font-medium text-slate-600">Total Revenue</p>
                  <p className="text-3xl font-bold text-slate-900">{formatCurrency(kpiData.totalRevenue)}</p>
                  <div className="flex items-center space-x-1 text-sm">
                    <TrendingUp className="w-4 h-4 text-purple-500" />
                    <span className="text-purple-600 font-medium">+25%</span>
                    <span className="text-slate-500">vs last month</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Pipeline Value */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center space-x-2">
                <PieChart className="w-5 h-5 text-primary" />
                <span>Pipeline Value</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-2xl font-bold text-slate-900">{formatCurrency(kpiData.pipelineCommissions)}</span>
                <Badge className="bg-primary/10 text-primary border-primary/20">
                  Expected Commission
                </Badge>
              </div>
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">Progress to Goal</span>
                  <span className="font-medium">75%</span>
                </div>
                <Progress value={75} className="h-2" />
                <p className="text-xs text-slate-500">Target: {formatCurrency(kpiData.pipelineCommissions * 1.33)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Average Deal Size */}
          <Card className="border-0 shadow-sm bg-white">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold flex items-center space-x-2">
                <Target className="w-5 h-5 text-indigo-600" />
                <span>Deal Analytics</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-slate-600">Avg Deal Size</p>
                  <p className="text-xl font-bold text-slate-900">{formatCurrency(kpiData.avgDeal)}</p>
                </div>
                <div>
                  <p className="text-sm text-slate-600">Conversion Rate</p>
                  <p className="text-xl font-bold text-slate-900">{kpiData.conversionRate.toFixed(1)}%</p>
                </div>
              </div>
              <div className="pt-2 border-t border-slate-100">
                <div className="flex items-center space-x-2 text-sm">
                  <Star className="w-4 h-4 text-yellow-500" />
                  <span className="text-slate-600">Performance trending upward</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Recent Listings - Takes up 2 columns on xl screens */}
          <Card className="xl:col-span-2 border-0 shadow-sm bg-white">
            <CardHeader className="border-b border-slate-100">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">Recent Listings</CardTitle>
                    <CardDescription>Your latest business opportunities</CardDescription>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm" className="hidden sm:flex">
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </Button>
                  <Button variant="ghost" size="sm" asChild>
                    <Link to="/listings">View All →</Link>
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y divide-slate-100">
                {listings.slice(0, 8).map((listing, index) => (
                  <div key={listing.id} className="p-4 hover:bg-slate-50/50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 min-w-0 flex-1">
                        <div className="w-10 h-10 bg-slate-100 rounded-lg flex items-center justify-center flex-shrink-0">
                          <Building2 className="w-5 h-5 text-slate-600" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h4 className="font-medium text-slate-900 truncate">
                            {listing.businessName || `Business ${index + 1}`}
                          </h4>
                          <p className="text-sm text-slate-500 truncate">
                            {listing.industry || 'Various Industry'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4 flex-shrink-0">
                        <div className="text-right hidden sm:block">
                          <p className="font-semibold text-slate-900">{formatCurrency(listing.askingPrice || 0)}</p>
                          <p className="text-xs text-slate-500">Asking Price</p>
                        </div>
                        <Badge className={`text-xs font-medium border ${getStatusColor(listing.status)}`}>
                          {listing.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions & Insights */}
          <div className="space-y-6">
            {/* Quick Actions */}
            <Card className="border-0 shadow-sm bg-white">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold flex items-center space-x-2">
                  <Zap className="w-5 h-5 text-orange-500" />
                  <span>Quick Actions</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 text-white" asChild>
                  <Link to="/listings/new">
                    <Plus className="w-4 h-4 mr-3" />
                    Create New Listing
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link to="/team">
                    <Users className="w-4 h-4 mr-3" />
                    Manage Team
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link to="/reports">
                    <BarChart3 className="w-4 h-4 mr-3" />
                    View Reports
                  </Link>
                </Button>
                <Button className="w-full justify-start" variant="outline" asChild>
                  <Link to="/settings">
                    <Settings className="w-4 h-4 mr-3" />
                    Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {/* Monthly Goals */}
            <Card className="border-0 shadow-sm bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg font-semibold flex items-center space-x-2">
                  <Award className="w-5 h-5 text-primary" />
                  <span>Monthly Goals</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium">Listings Target</span>
                      <span className="text-slate-600">{kpiData.activeListings}/20</span>
                    </div>
                    <Progress value={(kpiData.activeListings / 20) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium">Revenue Goal</span>
                      <span className="text-slate-600">{Math.round((kpiData.totalRevenue / 100000) * 100)}%</span>
                    </div>
                    <Progress value={(kpiData.totalRevenue / 100000) * 100} className="h-2" />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span className="font-medium">Deals Closed</span>
                      <span className="text-slate-600">{kpiData.closedThisMonth}/10</span>
                    </div>
                    <Progress value={(kpiData.closedThisMonth / 10) * 100} className="h-2" />
                  </div>
                </div>
                <div className="pt-3 border-t border-primary/20">
                  <div className="flex items-center space-x-2 text-sm">
                    <TrendingUp className="w-4 h-4 text-primary" />
                    <span className="text-primary font-medium">On track to exceed goals!</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard2;
