import React, { useState, useCallback, useEffect } from 'react';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/hooks/use-toast';
import { useCreateListingMutation, useUpdateListingMutation, useSaveDraftListingMutation } from '@/hooks/useQueryApi';
import type { CreateListingRequest, UpdateListingRequest, SaveDraftListingRequest } from '@/lib/api-client';
import type { ListingFormData } from '@/types';
import { parseCurrency, formatCurrencyInput } from '@/lib/formatters';
import { listingValidationSchema, getFieldDisplayName } from '@/models/listingValidation';
import { 
  Building2,
  DollarSign,
  FileText,
  TrendingUp,
  CheckCircle,
  ArrowLeft,
  ArrowRight,
  Save,
  Eye,
  Sparkles,
  MapPin,
  Calendar,
  Users,
  Clock,
  PieChart,
  Target,
  Briefcase,
  Award,
  Lightbulb,
  HandHeart
} from 'lucide-react';
import { Spinner } from '@/components/ui/spinner';
import { cn } from '@/lib/utils';

const industries = [
  'Restaurant',
  'Manufacturing', 
  'Retail',
  'Service',
  'Healthcare',
  'Auto',
  'Technology',
  'Other'
];

const statuses = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'confidential', label: 'Confidential' },
  { value: 'under_contract', label: 'Under Contract' },
  { value: 'sold', label: 'Sold' },
  { value: 'expired', label: 'Expired' },
  { value: 'withdrawn', label: 'Withdrawn' }
];

interface ListingFormProps {
  initialData?: Partial<ListingFormData>;
  isEditing?: boolean;
  listingId?: string;
}

type FormStep = 'basic' | 'financial' | 'description' | 'operations' | 'review';

const steps = [
  {
    id: 'basic' as FormStep,
    title: 'Basic Info',
    description: 'Essential business details',
    icon: Building2,
    fields: ['businessName', 'industry', 'askingPrice', 'cashFlow', 'status', 'location']
  },
  {
    id: 'financial' as FormStep,
    title: 'Financials',
    description: 'Revenue and financial data',
    icon: DollarSign,
    fields: ['annualRevenue', 'revenue2023', 'ebitda2023', 'inventoryValue', 'realEstateStatus']
  },
  {
    id: 'description' as FormStep,
    title: 'Description',
    description: 'Business overview and details',
    icon: FileText,
    fields: ['businessDescription', 'briefDescription', 'businessModel', 'keyFeatures']
  },
  {
    id: 'operations' as FormStep,
    title: 'Operations',
    description: 'Growth and operational details',
    icon: TrendingUp,
    fields: ['yearEstablished', 'employees', 'ownerHours', 'growthOpportunities', 'reasonForSale']
  },
  {
    id: 'review' as FormStep,
    title: 'Review',
    description: 'Final review and submit',
    icon: CheckCircle,
    fields: []
  }
];

export default function ListingForm2({ initialData = {}, isEditing = false, listingId }: ListingFormProps) {
  const navigate = useNavigate();
  const createListingMutation = useCreateListingMutation();
  const updateListingMutation = useUpdateListingMutation();
  const saveDraftListingMutation = useSaveDraftListingMutation();

  // Current step state
  const [currentStep, setCurrentStep] = useState<FormStep>('basic');
  const [completedSteps, setCompletedSteps] = useState<Set<FormStep>>(new Set());

  // Initialize form data with default values
  const [formData, setFormData] = useState<ListingFormData>({
    // Basic Information
    businessName: initialData.businessName || '',
    industry: initialData.industry || '',
    askingPrice: initialData.askingPrice || '',
    cashFlow: initialData.cashFlow || '',
    status: initialData.status || 'active',
    annualRevenue: initialData.annualRevenue || '',
    location: initialData.location || '',
    yearEstablished: initialData.yearEstablished || '',
    employees: initialData.employees || '',
    ownerHours: initialData.ownerHours || '',

    // Business Overview
    businessDescription: initialData.businessDescription || '',
    briefDescription: initialData.briefDescription || '',

    // Financial Details
    revenue2023: initialData.revenue2023 || '',
    ebitda2023: initialData.ebitda2023 || '',
    inventoryValue: initialData.inventoryValue || '',
    realEstateStatus: initialData.realEstateStatus || '',
    assetsIncluded: initialData.assetsIncluded || '',
    leaseDetails: initialData.leaseDetails || '',

    // Operations
    businessModel: initialData.businessModel || '',
    keyFeatures: initialData.keyFeatures || '',
    competitiveAdvantages: initialData.competitiveAdvantages || '',
    customerBase: initialData.customerBase || '',

    // Growth & Sale Information
    growthOpportunities: initialData.growthOpportunities || '',
    reasonForSale: initialData.reasonForSale || '',
    trainingPeriod: initialData.trainingPeriod || '',
    supportType: initialData.supportType || '',
    financingAvailable: initialData.financingAvailable || false,

    // Additional Details
    equipmentHighlights: initialData.equipmentHighlights || '',
    supplierRelationships: initialData.supplierRelationships || '',
    keyEmployeeInfo: initialData.keyEmployeeInfo || '',
    specialNotes: initialData.specialNotes || '',
  });

  // Validation state
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-save functionality
  useEffect(() => {
    const autoSaveTimer = setTimeout(() => {
      // Auto-save logic could go here
      console.log('Auto-saving form data...');
    }, 5000);

    return () => clearTimeout(autoSaveTimer);
  }, [formData]);

  // Validate a single field
  const validateField = useCallback((fieldName: keyof ListingFormData, value: any) => {
    try {
      const testData = { ...formData, [fieldName]: value };
      listingValidationSchema.parse(testData);
      
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
      
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const fieldError = error.errors.find(err => err.path[0] === fieldName);
        if (fieldError) {
          setValidationErrors(prev => ({
            ...prev,
            [fieldName]: fieldError.message
          }));
          return false;
        }
        
        setValidationErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldName];
          return newErrors;
        });
      }
      return true;
    }
  }, [formData]);

  // Validate current step
  const validateCurrentStep = useCallback(() => {
    const currentStepData = steps.find(step => step.id === currentStep);
    if (!currentStepData) return true;

    let isValid = true;
    const stepErrors: Record<string, string> = {};

    currentStepData.fields.forEach(fieldName => {
      const value = formData[fieldName as keyof ListingFormData];
      if (!validateField(fieldName as keyof ListingFormData, value)) {
        isValid = false;
      }
    });

    return isValid;
  }, [currentStep, formData, validateField]);

  // Generic input change handler
  const handleInputChange = useCallback((field: keyof ListingFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
    
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // Currency input change handler
  const handleCurrencyChange = useCallback((field: keyof ListingFormData, value: string) => {
    const formatted = formatCurrencyInput(value);
    setFormData(prev => ({
      ...prev,
      [field]: formatted,
    }));
    
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  }, [validationErrors]);

  // Step navigation
  const goToStep = useCallback((step: FormStep) => {
    if (validateCurrentStep()) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(step);
    }
  }, [currentStep, validateCurrentStep]);

  const goToNextStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    if (currentIndex < steps.length - 1 && validateCurrentStep()) {
      setCompletedSteps(prev => new Set([...prev, currentStep]));
      setCurrentStep(steps[currentIndex + 1].id);
    }
  }, [currentStep, validateCurrentStep]);

  const goToPreviousStep = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1].id);
    }
  }, [currentStep]);

  // Calculate progress
  const calculateProgress = useCallback(() => {
    const currentIndex = steps.findIndex(step => step.id === currentStep);
    return ((currentIndex + 1) / steps.length) * 100;
  }, [currentStep]);

  // AI generation handler
  const handleAiGenerate = useCallback(async (field: keyof ListingFormData) => {
    toast({
      title: "AI Generation",
      description: "AI content generation is coming soon!",
    });
  }, []);

  // Convert form data to API format (reusing logic from original form)
  const convertFormDataToApiFormat = useCallback((): CreateListingRequest | UpdateListingRequest => {
    const safeParseCurrency = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      return parseCurrency(value);
    };

    const safeParseInt = (value: string) => {
      if (!value || value.trim() === '') return undefined;
      const num = parseInt(value);
      return isNaN(num) ? undefined : num;
    };

    const baseRequest: CreateListingRequest | UpdateListingRequest = {
      businessName: formData.businessName.trim(),
      industry: formData.industry.trim(),
    };

    const askingPrice = safeParseCurrency(formData.askingPrice);
    if (askingPrice !== undefined) {
      baseRequest.askingPrice = askingPrice;
    }

    const cashFlow = safeParseCurrency(formData.cashFlow);
    if (cashFlow !== undefined) {
      baseRequest.cashFlowSde = cashFlow;
    }

    const annualRevenue = safeParseCurrency(formData.annualRevenue);
    if (annualRevenue !== undefined) {
      baseRequest.annualRevenue = annualRevenue;
    }

    if (formData.status) {
      baseRequest.status = formData.status;
    }

    if (formData.location && formData.location.trim()) {
      baseRequest.generalLocation = formData.location.trim();
    }

    const yearEstablished = safeParseInt(formData.yearEstablished);
    if (yearEstablished !== undefined) {
      baseRequest.yearEstablished = yearEstablished;
    }

    const employees = safeParseInt(formData.employees);
    if (employees !== undefined) {
      baseRequest.employees = employees;
    }

    const ownerHours = safeParseInt(formData.ownerHours);
    if (ownerHours !== undefined) {
      baseRequest.ownerHoursWeek = ownerHours;
    }

    // Build details object
    const details: any = {};
    let hasDetails = false;

    if (formData.businessDescription && formData.businessDescription.trim()) {
      details.businessDescription = formData.businessDescription.trim();
      hasDetails = true;
    }

    if (formData.briefDescription && formData.briefDescription.trim()) {
      details.briefDescription = formData.briefDescription.trim();
      hasDetails = true;
    }

    if (formData.businessModel && formData.businessModel.trim()) {
      details.operations = { businessModel: formData.businessModel.trim() };
      hasDetails = true;
    }

    if (formData.growthOpportunities && formData.growthOpportunities.trim()) {
      details.growthOpportunities = formData.growthOpportunities.split('\n').filter(line => line.trim() !== '');
      hasDetails = true;
    }

    if (formData.reasonForSale && formData.reasonForSale.trim()) {
      details.reasonForSale = formData.reasonForSale.trim();
      hasDetails = true;
    }

    details.financingAvailable = formData.financingAvailable;
    hasDetails = true;

    if (hasDetails) {
      baseRequest.details = details;
    }

    return baseRequest;
  }, [formData]);

  // Save as draft
  const handleSaveDraft = useCallback(async () => {
    setIsSubmitting(true);
    try {
      const apiData = convertFormDataToApiFormat();
      // Ensure status is set to draft for the draft endpoint
      apiData.status = 'draft';

      // Use the dedicated draft endpoint which handles incomplete data
      await saveDraftListingMutation.mutateAsync(apiData as SaveDraftListingRequest);

      toast({
        title: "Draft Saved",
        description: "Your listing has been saved as a draft.",
      });
    } catch (error) {
      toast({
        title: "Error Saving Draft",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [convertFormDataToApiFormat, saveDraftListingMutation]);

  // Save and view
  const handleSaveAndView = useCallback(async () => {
    setIsSubmitting(true);
    try {
      const apiData = convertFormDataToApiFormat();

      let result;
      if (isEditing && listingId) {
        result = await updateListingMutation.mutateAsync({
          listingId,
          listingData: apiData as UpdateListingRequest
        });
        navigate(`/listings/${listingId}`);
      } else {
        result = await createListingMutation.mutateAsync(apiData as CreateListingRequest);
        if (result?.data?.id) {
          navigate(`/listings/${result.data.id}`);
        } else {
          navigate('/listings');
        }
      }

      toast({
        title: isEditing ? "Listing Updated" : "Listing Created",
        description: isEditing ?
          "Your listing has been updated successfully." :
          "Your listing has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Error Saving Listing",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  }, [convertFormDataToApiFormat, isEditing, listingId, updateListingMutation, createListingMutation, navigate]);

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const progress = calculateProgress();

  return (
    <div className="max-w-4xl mx-auto space-y-8">
      {/* Progress Header */}
      <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {isEditing ? 'Edit Listing' : 'Create New Listing'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Step {currentStepIndex + 1} of {steps.length}: {steps[currentStepIndex].description}
              </p>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-500 mb-1">Progress</div>
              <div className="text-2xl font-bold text-blue-600">{Math.round(progress)}%</div>
            </div>
          </div>
          
          <Progress value={progress} className="h-2 mb-4" />
          
          {/* Step Navigation */}
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = step.id === currentStep;
              const isCompleted = completedSteps.has(step.id);
              const isAccessible = index <= currentStepIndex || isCompleted;
              
              return (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => isAccessible && goToStep(step.id)}
                    disabled={!isAccessible}
                    className={cn(
                      "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200",
                      isActive && "border-blue-500 bg-blue-500 text-white shadow-lg",
                      isCompleted && !isActive && "border-green-500 bg-green-500 text-white",
                      !isActive && !isCompleted && isAccessible && "border-gray-300 bg-white text-gray-600 hover:border-blue-300",
                      !isAccessible && "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed"
                    )}
                  >
                    {isCompleted && !isActive ? (
                      <CheckCircle className="w-5 h-5" />
                    ) : (
                      <Icon className="w-5 h-5" />
                    )}
                  </button>
                  
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "w-12 h-0.5 mx-2 transition-colors duration-200",
                      isCompleted ? "bg-green-500" : "bg-gray-200"
                    )} />
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-8">
          {/* Basic Information Step */}
          {currentStep === 'basic' && (
            <div className="space-y-8 animate-in fade-in-50 duration-300">
              <div className="text-center mb-8">
                <Building2 className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Basic Information
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Let's start with the essential details about your business
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="businessName" className="text-sm font-medium flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    Business Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="businessName"
                    value={formData.businessName}
                    onChange={(e) => handleInputChange('businessName', e.target.value)}
                    placeholder="Enter the business name"
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      validationErrors.businessName ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'
                    )}
                    disabled={isSubmitting}
                  />
                  {validationErrors.businessName && (
                    <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {validationErrors.businessName}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="industry" className="text-sm font-medium flex items-center gap-2">
                    <Briefcase className="w-4 h-4" />
                    Industry <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.industry}
                    onValueChange={(value) => handleInputChange('industry', value)}
                  >
                    <SelectTrigger className="h-12 text-base">
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      {industries.map(industry => (
                        <SelectItem key={industry} value={industry.toLowerCase()}>
                          {industry}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.industry && (
                    <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {validationErrors.industry}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="askingPrice" className="text-sm font-medium flex items-center gap-2">
                    <DollarSign className="w-4 h-4" />
                    Asking Price <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="askingPrice"
                    value={formData.askingPrice}
                    onChange={(e) => handleCurrencyChange('askingPrice', e.target.value)}
                    placeholder="$0"
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      validationErrors.askingPrice ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'
                    )}
                    disabled={isSubmitting}
                  />
                  {validationErrors.askingPrice && (
                    <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {validationErrors.askingPrice}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cashFlow" className="text-sm font-medium flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" />
                    Cash Flow/SDE <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="cashFlow"
                    value={formData.cashFlow}
                    onChange={(e) => handleCurrencyChange('cashFlow', e.target.value)}
                    placeholder="$0"
                    className={cn(
                      "h-12 text-base transition-all duration-200",
                      validationErrors.cashFlow ? 'border-red-500 focus:border-red-500' : 'focus:border-blue-500'
                    )}
                    disabled={isSubmitting}
                  />
                  {validationErrors.cashFlow && (
                    <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {validationErrors.cashFlow}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location" className="text-sm font-medium flex items-center gap-2">
                    <MapPin className="w-4 h-4" />
                    General Location
                  </Label>
                  <Input
                    id="location"
                    value={formData.location}
                    onChange={(e) => handleInputChange('location', e.target.value)}
                    placeholder="North Tampa, FL"
                    className="h-12 text-base focus:border-blue-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status" className="text-sm font-medium flex items-center gap-2">
                    <Target className="w-4 h-4" />
                    Status <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger className="h-12 text-base">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statuses.map(status => (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center gap-2">
                            <Badge variant={status.value === 'active' ? 'default' : 'secondary'} className="text-xs">
                              {status.label}
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {validationErrors.status && (
                    <p className="text-xs text-red-500 flex items-center gap-1 mt-1">
                      <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                      {validationErrors.status}
                    </p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                <Checkbox
                  id="confidential"
                  checked={formData.status === 'confidential'}
                  onCheckedChange={(checked) => handleInputChange('status', checked ? 'confidential' : 'active')}
                  disabled={isSubmitting}
                />
                <Label htmlFor="confidential" className="text-sm font-medium cursor-pointer">
                  Mark this listing as confidential
                </Label>
              </div>
            </div>
          )}

          {/* Financial Details Step */}
          {currentStep === 'financial' && (
            <div className="space-y-8 animate-in fade-in-50 duration-300">
              <div className="text-center mb-8">
                <DollarSign className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Financial Details
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Provide financial information to help buyers understand the business value
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div className="space-y-2">
                  <Label htmlFor="annualRevenue" className="text-sm font-medium flex items-center gap-2">
                    <PieChart className="w-4 h-4" />
                    Annual Revenue
                  </Label>
                  <Input
                    id="annualRevenue"
                    value={formData.annualRevenue}
                    onChange={(e) => handleCurrencyChange('annualRevenue', e.target.value)}
                    placeholder="$0"
                    className="h-12 text-base focus:border-green-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="revenue2023" className="text-sm font-medium flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    2023 Revenue
                  </Label>
                  <Input
                    id="revenue2023"
                    value={formData.revenue2023}
                    onChange={(e) => handleCurrencyChange('revenue2023', e.target.value)}
                    placeholder="$0"
                    className="h-12 text-base focus:border-green-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ebitda2023" className="text-sm font-medium flex items-center gap-2">
                    <TrendingUp className="w-4 h-4" />
                    2023 EBITDA
                  </Label>
                  <Input
                    id="ebitda2023"
                    value={formData.ebitda2023}
                    onChange={(e) => handleCurrencyChange('ebitda2023', e.target.value)}
                    placeholder="$0"
                    className="h-12 text-base focus:border-green-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inventoryValue" className="text-sm font-medium flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    Inventory Value
                  </Label>
                  <Input
                    id="inventoryValue"
                    value={formData.inventoryValue}
                    onChange={(e) => handleCurrencyChange('inventoryValue', e.target.value)}
                    placeholder="$0"
                    className="h-12 text-base focus:border-green-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="realEstateStatus" className="text-sm font-medium flex items-center gap-2">
                    <Building2 className="w-4 h-4" />
                    Real Estate Status
                  </Label>
                  <Select
                    value={formData.realEstateStatus}
                    onValueChange={(value) => handleInputChange('realEstateStatus', value)}
                  >
                    <SelectTrigger className="h-12 text-base">
                      <SelectValue placeholder="Select real estate status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="leased">Leased</SelectItem>
                      <SelectItem value="owned">Owned</SelectItem>
                      <SelectItem value="available-separately">Available Separately</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {/* Description Step */}
          {currentStep === 'description' && (
            <div className="space-y-8 animate-in fade-in-50 duration-300">
              <div className="text-center mb-8">
                <FileText className="w-12 h-12 text-purple-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Business Description
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Tell the story of your business and what makes it special
                </p>
              </div>

              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="businessDescription" className="text-sm font-medium flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Detailed Business Description
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAiGenerate('businessDescription')}
                      className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
                      disabled={isSubmitting}
                    >
                      <Sparkles className="h-4 w-4" />
                      Generate with AI
                    </Button>
                  </div>
                  <Textarea
                    id="businessDescription"
                    value={formData.businessDescription}
                    onChange={(e) => handleInputChange('businessDescription', e.target.value)}
                    placeholder="Provide a comprehensive description of the business, its operations, and what makes it unique..."
                    rows={6}
                    className="text-base resize-none focus:border-purple-500"
                    disabled={isSubmitting}
                  />
                  <div className="flex justify-between items-center">
                    {validationErrors.businessDescription && (
                      <p className="text-xs text-red-500 flex items-center gap-1">
                        <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                        {validationErrors.businessDescription}
                      </p>
                    )}
                    <p className="text-xs text-gray-500 ml-auto">
                      {formData.businessDescription.length}/5,000 characters
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="briefDescription" className="text-sm font-medium flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    Brief Description for Portfolio
                  </Label>
                  <Textarea
                    id="briefDescription"
                    value={formData.briefDescription}
                    onChange={(e) => handleInputChange('briefDescription', e.target.value)}
                    placeholder="A concise summary for portfolio listings..."
                    rows={3}
                    className="text-base resize-none focus:border-purple-500"
                    disabled={isSubmitting}
                  />
                  <div className="flex justify-between items-center">
                    {validationErrors.briefDescription && (
                      <p className="text-xs text-red-500 flex items-center gap-1">
                        <span className="w-1 h-1 bg-red-500 rounded-full"></span>
                        {validationErrors.briefDescription}
                      </p>
                    )}
                    <p className="text-xs text-gray-500 ml-auto">
                      {formData.briefDescription.length}/500 characters
                    </p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="businessModel" className="text-sm font-medium flex items-center gap-2">
                      <Briefcase className="w-4 h-4" />
                      Business Model
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAiGenerate('businessModel')}
                      className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
                      disabled={isSubmitting}
                    >
                      <Sparkles className="h-4 w-4" />
                      Generate with AI
                    </Button>
                  </div>
                  <Textarea
                    id="businessModel"
                    value={formData.businessModel}
                    onChange={(e) => handleInputChange('businessModel', e.target.value)}
                    placeholder="Describe how the business operates and generates revenue..."
                    rows={4}
                    className="text-base resize-none focus:border-purple-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="keyFeatures" className="text-sm font-medium flex items-center gap-2">
                    <Award className="w-4 h-4" />
                    Key Features & Highlights
                  </Label>
                  <Textarea
                    id="keyFeatures"
                    value={formData.keyFeatures}
                    onChange={(e) => handleInputChange('keyFeatures', e.target.value)}
                    placeholder="• Feature 1&#10;• Feature 2&#10;• Feature 3"
                    rows={4}
                    className="text-base resize-none focus:border-purple-500"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Operations Step */}
          {currentStep === 'operations' && (
            <div className="space-y-8 animate-in fade-in-50 duration-300">
              <div className="text-center mb-8">
                <TrendingUp className="w-12 h-12 text-orange-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Operations & Growth
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Share operational details and growth opportunities
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div className="space-y-2">
                  <Label htmlFor="yearEstablished" className="text-sm font-medium flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    Year Established
                  </Label>
                  <Input
                    id="yearEstablished"
                    type="number"
                    min="1800"
                    max="2025"
                    value={formData.yearEstablished}
                    onChange={(e) => handleInputChange('yearEstablished', e.target.value)}
                    placeholder="2020"
                    className="h-12 text-base focus:border-orange-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employees" className="text-sm font-medium flex items-center gap-2">
                    <Users className="w-4 h-4" />
                    Number of Employees
                  </Label>
                  <Input
                    id="employees"
                    type="number"
                    min="0"
                    value={formData.employees}
                    onChange={(e) => handleInputChange('employees', e.target.value)}
                    placeholder="5"
                    className="h-12 text-base focus:border-orange-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="ownerHours" className="text-sm font-medium flex items-center gap-2">
                    <Clock className="w-4 h-4" />
                    Owner Hours/Week
                  </Label>
                  <Input
                    id="ownerHours"
                    type="number"
                    min="0"
                    max="168"
                    value={formData.ownerHours}
                    onChange={(e) => handleInputChange('ownerHours', e.target.value)}
                    placeholder="40"
                    className="h-12 text-base focus:border-orange-500"
                    disabled={isSubmitting}
                  />
                </div>
              </div>

              <div className="space-y-8">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="growthOpportunities" className="text-sm font-medium flex items-center gap-2">
                      <Lightbulb className="w-4 h-4" />
                      Growth Opportunities
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAiGenerate('growthOpportunities')}
                      className="flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50"
                      disabled={isSubmitting}
                    >
                      <Sparkles className="h-4 w-4" />
                      Generate with AI
                    </Button>
                  </div>
                  <Textarea
                    id="growthOpportunities"
                    value={formData.growthOpportunities}
                    onChange={(e) => handleInputChange('growthOpportunities', e.target.value)}
                    placeholder="Describe expansion possibilities, market potential, and untapped revenue streams..."
                    rows={4}
                    className="text-base resize-none focus:border-orange-500"
                    disabled={isSubmitting}
                  />
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="reasonForSale" className="text-sm font-medium flex items-center gap-2">
                      <HandHeart className="w-4 h-4" />
                      Reason for Sale
                    </Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAiGenerate('reasonForSale')}
                      className="flex items-center gap-2 text-orange-600 border-orange-200 hover:bg-orange-50"
                      disabled={isSubmitting}
                    >
                      <Sparkles className="h-4 w-4" />
                      Generate with AI
                    </Button>
                  </div>
                  <Textarea
                    id="reasonForSale"
                    value={formData.reasonForSale}
                    onChange={(e) => handleInputChange('reasonForSale', e.target.value)}
                    placeholder="Share the seller's motivation and transition timeline..."
                    rows={3}
                    className="text-base resize-none focus:border-orange-500"
                    disabled={isSubmitting}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Review Step */}
          {currentStep === 'review' && (
            <div className="space-y-8 animate-in fade-in-50 duration-300">
              <div className="text-center mb-8">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                  Review & Submit
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Review your listing details before submitting
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Basic Information Summary */}
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Building2 className="w-5 h-5 text-blue-500" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Business Name:</span>
                      <span className="text-sm font-medium">{formData.businessName || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Industry:</span>
                      <span className="text-sm font-medium capitalize">{formData.industry || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Asking Price:</span>
                      <span className="text-sm font-medium">{formData.askingPrice || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Cash Flow:</span>
                      <span className="text-sm font-medium">{formData.cashFlow || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Status:</span>
                      <Badge variant={formData.status === 'active' ? 'default' : 'secondary'}>
                        {statuses.find(s => s.value === formData.status)?.label || formData.status}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                {/* Financial Summary */}
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <DollarSign className="w-5 h-5 text-green-500" />
                      Financial Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Annual Revenue:</span>
                      <span className="text-sm font-medium">{formData.annualRevenue || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">2023 Revenue:</span>
                      <span className="text-sm font-medium">{formData.revenue2023 || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">2023 EBITDA:</span>
                      <span className="text-sm font-medium">{formData.ebitda2023 || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Inventory Value:</span>
                      <span className="text-sm font-medium">{formData.inventoryValue || 'Not provided'}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Operations Summary */}
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <TrendingUp className="w-5 h-5 text-orange-500" />
                      Operations
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Year Established:</span>
                      <span className="text-sm font-medium">{formData.yearEstablished || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Employees:</span>
                      <span className="text-sm font-medium">{formData.employees || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Owner Hours/Week:</span>
                      <span className="text-sm font-medium">{formData.ownerHours || 'Not provided'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Location:</span>
                      <span className="text-sm font-medium">{formData.location || 'Not provided'}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Description Summary */}
                <Card className="border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="w-5 h-5 text-purple-500" />
                      Description
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-600">Business Description:</span>
                      <p className="text-sm mt-1 line-clamp-3">
                        {formData.businessDescription || 'Not provided'}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-gray-600">Brief Description:</span>
                      <p className="text-sm mt-1 line-clamp-2">
                        {formData.briefDescription || 'Not provided'}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Completion Status */}
              <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg p-6">
                <div className="flex items-center gap-3 mb-4">
                  <CheckCircle className="w-6 h-6 text-green-600" />
                  <h4 className="text-lg font-semibold text-green-800 dark:text-green-200">
                    Ready to Submit
                  </h4>
                </div>
                <p className="text-green-700 dark:text-green-300 mb-4">
                  Your listing is complete and ready to be submitted. You can always edit it later if needed.
                </p>
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-700 dark:text-green-300">All required fields completed</span>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              {currentStepIndex > 0 && (
                <Button
                  variant="outline"
                  onClick={goToPreviousStep}
                  disabled={isSubmitting}
                  className="flex items-center gap-2"
                >
                  <ArrowLeft className="w-4 h-4" />
                  Previous
                </Button>
              )}
            </div>

            <div className="flex items-center gap-3">
              {/* Save Draft Button */}
              <Button
                variant="outline"
                onClick={handleSaveDraft}
                disabled={isSubmitting}
                className="flex items-center gap-2"
              >
                {isSubmitting ? (
                  <Spinner className="w-4 h-4" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                Save Draft
              </Button>

              {/* Next/Submit Button */}
              {currentStep !== 'review' ? (
                <Button
                  onClick={goToNextStep}
                  disabled={isSubmitting}
                  className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
                >
                  Next
                  <ArrowRight className="w-4 h-4" />
                </Button>
              ) : (
                <div className="flex items-center gap-2">
                  <Button
                    onClick={handleSaveAndView}
                    disabled={isSubmitting}
                    className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
                  >
                    {isSubmitting ? (
                      <Spinner className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                    {isEditing ? 'Update & View' : 'Create & View'}
                  </Button>
                </div>
              )}
            </div>
          </div>

          {/* Step Indicator */}
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center justify-center gap-2 text-sm text-gray-500">
              <span>Step {currentStepIndex + 1} of {steps.length}</span>
              <span>•</span>
              <span>{steps[currentStepIndex].title}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
