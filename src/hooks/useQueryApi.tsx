import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { apiClient, type Listing, type CreateListingRequest, type UpdateListingRequest, type SaveDraftListingRequest } from '@/lib/api-client'
import { useAuth } from '@/contexts/AuthContext'
import {
  queryKeys,
  handleMutationSuccess,
  handleMutationError,
  useInvalidateQueries,
  useOptimisticUpdates
} from '@/lib/query-client'

// Types
interface UseListingsParams {
  page?: number
  limit?: number
  status?: string
  industry?: string
  search?: string
  location?: string
  assignedTo?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'created_at' | 'updated_at' | 'asking_price' | 'business_name' | 'date_listed' | 'days_listed'
  sortOrder?: 'asc' | 'desc'
}

interface MutationOptions {
  showSuccessToast?: boolean
  successMessage?: string
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

// QUERIES

// Hook for fetching listings
export function useListListingsQuery(params: UseListingsParams = {}, enabled = true) {
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: queryKeys.listings(params),
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.getListings(accessToken, {
        page: params.page || 1,
        limit: params.limit || 20,
        status: params.status,
        industry: params.industry,
        search: params.search,
        location: params.location,
        assignedTo: params.assignedTo,
        minPrice: params.minPrice,
        maxPrice: params.maxPrice,
        sortBy: params.sortBy || 'created_at',
        sortOrder: params.sortOrder || 'desc',
      })
    },
    enabled: enabled && !!accessToken,
    staleTime: 2 * 60 * 1000, // 2 minutes for listings
  })
}

// Hook for fetching a single listing
export function useGetListingDetailsQuery(listingId: string | undefined, includeDetails = true) {
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: queryKeys.listing(listingId!),
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      const response = await apiClient.getListing(accessToken, listingId!, includeDetails)
      return response; // Return the ListingResponse directly (no need to extract .data)
    },
    enabled: !!listingId && !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes for individual listings
  })
}

// Hook for fetching user profile
export function useUserProfileQuery(enabled = true) {
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: queryKeys.userProfile(),
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.getUserProfile(accessToken)
    },
    enabled: enabled && !!accessToken,
    staleTime: 10 * 60 * 1000, // 10 minutes for user profile
  })
}

// Hook for fetching user profile (team data would be handled separately)
export function useUserWorkspaceQuery(enabled = true) {
  const { accessToken } = useAuth();

  return useQuery({
    queryKey: ['user', 'workspace'],
    queryFn: async () => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.getUserProfile(accessToken)
    },
    enabled: enabled && !!accessToken,
    staleTime: 5 * 60 * 1000, // 5 minutes for workspace data
  })
}

// MUTATIONS

// Hook for creating listings
export function useCreateListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { addListingToCache } = useOptimisticUpdates()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Listing created successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingData: CreateListingRequest) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.createListing(accessToken, listingData)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Add to cache optimistically
      if (data) {
        addListingToCache(data)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating listings
export function useUpdateListingMutation(options: MutationOptions = {}) {
  const { invalidateListings, invalidateListing } = useInvalidateQueries()
  const { updateListingData, updateListingsData } = useOptimisticUpdates()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Listing updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ listingId, listingData }: { listingId: string; listingData: UpdateListingRequest }) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.updateListing(accessToken, listingId, listingData)
    },
    onSuccess: (data, variables) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      
      // Update cache optimistically
      if (data) {
        // Update individual listing cache
        updateListingData(variables.listingId, () => data)
        
        // Update listings list cache
        updateListingsData((oldData: any) => {
          if (!oldData?.data) return oldData
          return {
            ...oldData,
            data: oldData.data.map((listing: any) => 
              listing.id === variables.listingId ? data : listing
            ),
          }
        })
      }
      
      // Invalidate to ensure consistency
      invalidateListing(variables.listingId)
      invalidateListings()
      
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for saving draft listings
export function useSaveDraftListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { addListingToCache } = useOptimisticUpdates()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Draft saved successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingData: SaveDraftListingRequest) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.saveDraftListing(accessToken, listingData)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Add to cache optimistically
      if (data) {
        addListingToCache(data)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for deleting listings
export function useDeleteListingMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { removeListingFromCache } = useOptimisticUpdates()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Listing deleted successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (listingId: string) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.deleteListing(accessToken, listingId)
    },
    onSuccess: (data, listingId) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Remove from cache optimistically
      removeListingFromCache(listingId)

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating listing status
export function useUpdateListingStatusMutation(options: MutationOptions = {}) {
  const { invalidateListings, invalidateListing } = useInvalidateQueries()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Listing status updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ listingId, statusUpdate }: { listingId: string; statusUpdate: { status: string; reason?: string; notes?: string } }) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.updateListingStatus(accessToken, listingId, statusUpdate)
    },
    onSuccess: (data, variables) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate related queries
      invalidateListing(variables.listingId)
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for updating user profile
export function useUpdateUserProfileMutation(options: MutationOptions = {}) {
  const { invalidateUserProfile } = useInvalidateQueries()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'Profile updated successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (profileData: any) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.updateUserProfile(accessToken, profileData)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate user profile to refetch fresh data
      invalidateUserProfile()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for file uploads
export function useUploadFileMutation(options: MutationOptions = {}) {
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'File uploaded successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async ({ file, options }: { file: File; options: { fileType: 'document' | 'image' | 'video' | 'audio' | 'other'; entityType?: string; entityId?: string; isPublic?: boolean } }) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.uploadFile(accessToken, file, options)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for file deletion
export function useDeleteFileMutation(options: MutationOptions = {}) {
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'File deleted successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (fileId: string) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.deleteFile(accessToken, fileId)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }
      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// Hook for CSV bulk import
export function useImportListingsCsvMutation(options: MutationOptions = {}) {
  const { invalidateListings } = useInvalidateQueries()
  const { accessToken } = useAuth()

  const {
    showSuccessToast = true,
    successMessage = 'CSV import completed successfully',
    onSuccess,
    onError,
  } = options

  return useMutation({
    mutationFn: async (file: File) => {
      if (!accessToken) {
        throw new Error('No access token available. Please log in.');
      }
      return apiClient.importListingsFromCSV(accessToken, file)
    },
    onSuccess: (data) => {
      if (showSuccessToast) {
        handleMutationSuccess(successMessage)
      }

      // Invalidate listings to ensure fresh data
      invalidateListings()

      if (onSuccess) onSuccess(data)
    },
    onError: (error) => {
      if (showSuccessToast) {
        handleMutationError(error)
      }
      if (onError) onError(error)
    },
  })
}

// CONVENIENCE HOOKS

// Combined hook for listings operations (replaces useListings)
export function useListings(params: UseListingsParams = {}) {
  const listingsQuery = useListListingsQuery(params)
  const createMutation = useCreateListingMutation()
  const updateMutation = useUpdateListingMutation()
  const deleteMutation = useDeleteListingMutation()
  const updateStatusMutation = useUpdateListingStatusMutation()

  return {
    // Data
    listings: listingsQuery.data?.data || [],
    pagination: listingsQuery.data?.pagination || { page: 1, limit: 20, total: 0, pages: 0 },
    
    // Loading states
    loading: listingsQuery.isLoading,
    isRefetching: listingsQuery.isRefetching,
    
    // Error states
    error: listingsQuery.error,
    
    // Query state
    isSuccess: listingsQuery.isSuccess,
    isError: listingsQuery.isError,
    
    // Functions
    refetch: listingsQuery.refetch,
    
    // Mutations
    createListing: createMutation.mutate,
    updateListing: (listingId: string, listingData: UpdateListingRequest) => 
      updateMutation.mutate({ listingId, listingData }),
    deleteListing: deleteMutation.mutate,
    updateListingStatus: (listingId: string, status: string, reason?: string, notes?: string) => 
      updateStatusMutation.mutate({ listingId, statusUpdate: { status, reason, notes } }),
    
    // Mutation states
    mutations: {
      creating: createMutation.isPending,
      updating: updateMutation.isPending,
      deleting: deleteMutation.isPending,
      updatingStatus: updateStatusMutation.isPending,
      
      createError: createMutation.error,
      updateError: updateMutation.error,
      deleteError: deleteMutation.error,
      statusError: updateStatusMutation.error,
    },
  }
}

// Single listing hook (replaces useListing)
export function useListing(listingId: string | undefined) {
  const listingQuery = useGetListingDetailsQuery(listingId)
  
  return {
    listing: listingQuery.data || null,
    loading: listingQuery.isLoading,
    error: listingQuery.error,
    isSuccess: listingQuery.isSuccess,
    isError: listingQuery.isError,
    refetch: listingQuery.refetch,
  }
} 