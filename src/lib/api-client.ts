import { API_CONFIG } from './api-config';
import { TypedApiClient } from './generated-api-client';
import type { components } from '@/types/api';

// Re-export the types from the generated schema
export type User = components['schemas']['User'];
export type Workspace = components['schemas']['Workspace'];
export type UserProfile = components['schemas']['UserProfileResponse'];
export type AuthSession = components['schemas']['AuthSession'];

// Auth types
export type SignUpRequest = components['schemas']['SignUpRequest'];
export type SignUpResponse = components['schemas']['SignUpResponse'];
export type SignInRequest = components['schemas']['SignInRequest'];
export type SignInResponse = components['schemas']['SignInResponse'];
export type SignOutRequest = components['schemas']['SignOutRequest'];
export type SignOutResponse = components['schemas']['SignOutResponse'];
export type RefreshRequest = components['schemas']['RefreshRequest'];
export type RefreshResponse = components['schemas']['RefreshResponse'];
export type ForgotPasswordRequest = components['schemas']['ForgotPasswordRequest'];
export type ForgotPasswordResponse = components['schemas']['ForgotPasswordResponse'];
export type ResetPasswordRequest = components['schemas']['ResetPasswordRequest'];
export type ResetPasswordResponse = components['schemas']['ResetPasswordResponse'];
export type UpdateProfileRequest = components['schemas']['UpdateProfileRequest'];
export type UpdateProfileResponse = components['schemas']['UpdateProfileResponse'];
export type VerifyEmailRequest = components['schemas']['VerifyEmailRequest'];
export type VerifyEmailResponse = components['schemas']['VerifyEmailResponse'];

// File types
export type File = components['schemas']['File'];
export type UploadFileRequest = components['schemas']['UploadFileRequest'];
export type UploadFileResponse = components['schemas']['UploadFileResponse'];
export type GetFileResponse = components['schemas']['GetFileResponse'];
export type DeleteResponse = components['schemas']['DeleteResponse'];

// Listing types
export type ListingResponse = components['schemas']['ListingResponse'];
export type ListingListResponse = components['schemas']['ListingListResponse'];
export type SingleListingResponse = components['schemas']['SingleListingResponse'];
export type CreateListingRequest = components['schemas']['CreateListingRequest'];
export type UpdateListingRequest = components['schemas']['UpdateListingRequest'];
export type SaveDraftListingRequest = components['schemas']['SaveDraftListingRequest'];
export type BulkCreateResponse = components['schemas']['BulkCreateResponse'];

// Workspace types
export type SingleWorkspaceResponse = components['schemas']['SingleWorkspaceResponse'];
export type WorkspaceUpdateResponse = components['schemas']['WorkspaceUpdateResponse'];
export type UpdateWorkspaceRequest = components['schemas']['UpdateWorkspaceRequest'];
export type WorkspaceInvitation = components['schemas']['WorkspaceInvitation'];
export type WorkspaceInvitationListResponse = components['schemas']['WorkspaceInvitationListResponse'];
export type SingleWorkspaceInvitationResponse = components['schemas']['SingleWorkspaceInvitationResponse'];
export type CreateWorkspaceInvitationRequest = components['schemas']['CreateWorkspaceInvitationRequest'];
export type UpdateWorkspaceInvitationRequest = components['schemas']['UpdateWorkspaceInvitationRequest'];

// Convenient type alias
export type Listing = ListingResponse;

// Legacy types for backward compatibility (remove once all usage is updated)
export interface UserWorkspacesResponse {
  workspaces: {
    workspace_id: string;
    workspace_name: string;
    company_name: string;
    user_role: string;
    is_active: boolean;
    status: string;
    subscription_plan: string;
    logo_url?: string;
    primary_color?: string;
  }[];
}

export interface SwitchWorkspaceRequest {
  workspace_id: string;
}

export interface SwitchWorkspaceResponse {
  success: boolean;
  workspace: {
    id: string;
    companyName: string;
    companyType: string;
    subscriptionPlan: string;
    status: string;
    createdAt: string;
  };
  profile: {
    id: string;
    workspaceId: string;
    role: string;
    firstName: string;
    lastName: string;
    displayName: string;
    email: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Type-safe API client that wraps the generated client
class ApiClient {
  private client: TypedApiClient;

  constructor() {
    this.client = new TypedApiClient({
      baseUrl: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
    });
  }

  // Helper to extract data from API response
  private extractData<T>(response: { data: T }): T {
    return response.data;
  }

  // Helper to handle API errors
  private async handleRequest<T>(request: Promise<{ data: T }>): Promise<T> {
    try {
      const response = await request;
      return this.extractData(response);
    } catch (error) {
      // Convert the error to our ApiError format
      if (error instanceof Error) {
        const apiError: ApiError = {
          message: error.message,
        };
        
        // Extract status code if available in the error message
        const statusMatch = error.message.match(/HTTP (\d+):/);
        if (statusMatch) {
          apiError.status = parseInt(statusMatch[1], 10);
        }
        
        throw apiError;
      }
      throw error;
    }
  }

  // Authentication methods
  async signUp(data: SignUpRequest): Promise<SignUpResponse> {
    return this.handleRequest(this.client.signUp(data));
  }

  async signIn(data: SignInRequest): Promise<SignInResponse> {
    return this.handleRequest(this.client.signIn(data));
  }

  async signOut(data: SignOutRequest): Promise<SignOutResponse> {
    return this.handleRequest(this.client.signOut(data));
  }

  async refreshToken(data: RefreshRequest): Promise<RefreshResponse> {
    return this.handleRequest(this.client.refreshToken(data));
  }
  
  async forgotPassword(data: ForgotPasswordRequest): Promise<ForgotPasswordResponse> {
    return this.handleRequest(this.client.forgotPassword(data));
  }

  async resetPassword(data: ResetPasswordRequest): Promise<ResetPasswordResponse> {
    return this.handleRequest(this.client.resetPassword(data));
  }

  // User Profile methods
  async getUserProfile(accessToken: string): Promise<UserProfile> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.getUserProfile());
  }

  async updateUserProfile(
    accessToken: string,
    data: UpdateProfileRequest
  ): Promise<UpdateProfileResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.updateUserProfile(data));
  }

  async verifyEmail(data: VerifyEmailRequest): Promise<VerifyEmailResponse> {
    return this.handleRequest(this.client.verifyEmail(data));
  }

  // Workspace methods
  async getCurrentWorkspace(accessToken: string): Promise<Workspace> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.getCurrentWorkspace());
    return response.data;
  }

  async updateCurrentWorkspace(
    accessToken: string,
    data: UpdateWorkspaceRequest
  ): Promise<WorkspaceUpdateResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.updateCurrentWorkspace(data));
  }

  // Workspace Invitation methods
  async getWorkspaceInvitations(
    accessToken: string,
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      role?: "owner" | "admin" | "member" | "viewer";
    }
  ): Promise<WorkspaceInvitationListResponse> {
    this.client.setAuth(accessToken);
    
    const queryParams: Record<string, string | number> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams[key] = String(value);
        }
      });
    }

    return this.handleRequest(this.client.getWorkspaceInvitations(queryParams));
  }

  async createWorkspaceInvitation(
    accessToken: string,
    data: CreateWorkspaceInvitationRequest
  ): Promise<WorkspaceInvitation> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.createWorkspaceInvitation(data));
    return response.data;
  }

  async updateWorkspaceInvitation(
    accessToken: string,
    invitationId: string,
    data: UpdateWorkspaceInvitationRequest
  ): Promise<WorkspaceInvitation> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.updateWorkspaceInvitation(invitationId, data));
    return response.data;
  }

  async deleteWorkspaceInvitation(
    accessToken: string,
    invitationId: string
  ): Promise<DeleteResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.deleteWorkspaceInvitation(invitationId));
  }

  async resendWorkspaceInvitation(
    accessToken: string,
    invitationId: string
  ): Promise<WorkspaceInvitation> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.resendWorkspaceInvitation(invitationId));
    return response.data;
  }

  // Legacy workspace methods (for backward compatibility)
  async getUserWorkspaces(accessToken: string): Promise<UserWorkspacesResponse> {
    this.client.setAuth(accessToken);
    // This might need to be implemented differently based on actual API structure
    const response = await this.client.get('/profile');
    return this.extractData(response) as UserWorkspacesResponse;
  }

  async switchWorkspace(
    accessToken: string, 
    data: SwitchWorkspaceRequest
  ): Promise<SwitchWorkspaceResponse> {
    this.client.setAuth(accessToken);
    // This endpoint might need to be custom implemented
    const response = await this.client.post('/workspaces/switch', data);
    return this.extractData(response) as SwitchWorkspaceResponse;
  }

  // File Upload methods
  async uploadFile(
    accessToken: string,
    file: File,
    options: {
      fileType: 'document' | 'image' | 'video' | 'audio' | 'other';
      entityType?: string;
      entityId?: string;
      isPublic?: boolean;
    }
  ): Promise<UploadFileResponse> {
    this.client.setAuth(accessToken);
    
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', options.fileType);
    
    if (options.entityType) {
      formData.append('entityType', options.entityType);
    }
    if (options.entityId) {
      formData.append('entityId', options.entityId);
    }
    if (options.isPublic !== undefined) {
      formData.append('isPublic', String(options.isPublic));
    }

    return this.handleRequest(this.client.uploadFile(formData));
  }

  async getFile(accessToken: string, fileId: string): Promise<GetFileResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.getFile(fileId));
  }

  async deleteFile(accessToken: string, fileId: string): Promise<DeleteResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.deleteFile(fileId));
  }

  // Listing methods
  async getListings(
    accessToken: string,
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      industry?: string;
      assignedTo?: string;
      minPrice?: number;
      maxPrice?: number;
      location?: string;
      sortBy?: 'created_at' | 'updated_at' | 'asking_price' | 'business_name' | 'date_listed' | 'days_listed';
      sortOrder?: 'asc' | 'desc';
      search?: string;
    }
  ): Promise<ListingListResponse> {
    this.client.setAuth(accessToken);
    
    // Convert params to the format expected by the generated client
    const queryParams: Record<string, string | number> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams[key] = String(value);
        }
      });
    }

    return this.handleRequest(this.client.getListings(queryParams));
  }

  async getListing(accessToken: string, listingId: string, includeDetails: boolean = true): Promise<ListingResponse> {
    this.client.setAuth(accessToken);
    const params = includeDetails ? { includeDetails: 'true' } : {};
    const response = await this.handleRequest(this.client.getListing(listingId, params));
    return response.data;
  }

  async createListing(accessToken: string, listing: CreateListingRequest): Promise<ListingResponse> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.createListing(listing));
    return response.data;
  }

  async updateListing(accessToken: string, listingId: string, listing: UpdateListingRequest): Promise<ListingResponse> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.updateListing(listingId, listing));
    return response.data;
  }

  async deleteListing(accessToken: string, listingId: string): Promise<DeleteResponse> {
    this.client.setAuth(accessToken);
    return this.handleRequest(this.client.deleteListing(listingId));
  }

  async saveDraftListing(accessToken: string, listing: SaveDraftListingRequest): Promise<ListingResponse> {
    this.client.setAuth(accessToken);
    const response = await this.handleRequest(this.client.saveDraftListing(listing));
    return response.data;
  }

  // CSV import for listings
  async importListingsFromCSV(accessToken: string, file: File): Promise<BulkCreateResponse> {
    this.client.setAuth(accessToken);
    
    const formData = new FormData();
    formData.append('file', file);

    return this.handleRequest(this.client.uploadListingsCsv(formData));
  }

  // Legacy methods (for backward compatibility)
  async updateListingStatus(
    accessToken: string, 
    listingId: string, 
    statusUpdate: any
  ): Promise<any> {
    this.client.setAuth(accessToken);
    // This endpoint might need to be added to the generated client
    const response = await this.client.patch(`/v1/listings/${listingId}/status`, statusUpdate);
    return this.extractData(response);
  }

  async getListingStatusHistory(accessToken: string, listingId: string): Promise<any> {
    this.client.setAuth(accessToken);
    // This endpoint might need to be added to the generated client
    const response = await this.client.get(`/v1/listings/${listingId}/status-history`);
    return this.extractData(response);
  }

  async createBulkListings(accessToken: string, listings: any): Promise<BulkCreateResponse> {
    this.client.setAuth(accessToken);
    const response = await this.client.post('/v1/listings/bulk/csv', listings);
    return this.extractData(response) as BulkCreateResponse;
  }

  // Utility method to make authenticated requests (for endpoints not in generated client)
  async makeAuthenticatedRequest<T>(
    url: string,
    accessToken: string,
    options: RequestInit = {}
  ): Promise<T> {
    this.client.setAuth(accessToken);
    
    const method = (options.method || 'GET').toLowerCase();
    const body = options.body ? JSON.parse(options.body as string) : undefined;

    let response: any;
    switch (method) {
      case 'get':
        response = await this.client.get(url);
        break;
      case 'post':
        response = await this.client.post(url, body);
        break;
      case 'put':
        response = await this.client.put(url, body);
        break;
      case 'delete':
        response = await this.client.delete(url);
        break;
      case 'patch':
        response = await this.client.patch(url, body);
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }
    
    return this.extractData(response) as T;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Helper function to check if error is an API error
export const isApiError = (error: unknown): error is ApiError => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as any).message === 'string'
  );
}; 