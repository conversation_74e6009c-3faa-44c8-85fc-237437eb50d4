# Draft Listing Implementation

## Overview

This document describes the implementation of the new `/v1/listings/draft` API endpoint integration for saving listing drafts with incomplete data.

## Changes Made

### 1. API Client Updates

#### Generated API Client (`src/lib/generated-api-client.ts`)
- Added `saveDraftListing` method to the `TypedApiClient` class
- Method accepts `SaveDraftListingRequest` data and returns `SingleListingResponse`
- Endpoint: `POST /v1/listings/draft`

```typescript
async saveDraftListing(data: components["schemas"]["SaveDraftListingRequest"]) {
  return this.post<components["schemas"]["SingleListingResponse"]>(
    '/v1/listings/draft',
    data
  );
}
```

#### Main API Client (`src/lib/api-client.ts`)
- Added `SaveDraftListingRequest` type export
- Added `saveDraftListing` method that wraps the generated client method
- Handles authentication and response processing

```typescript
async saveDraftListing(accessToken: string, listing: SaveDraftListingRequest): Promise<ListingResponse> {
  this.client.setAuth(accessToken);
  const response = await this.handleRequest(this.client.saveDraftListing(listing));
  return response.data;
}
```

### 2. React Query Hook

#### New Hook (`src/hooks/useQueryApi.tsx`)
- Added `useSaveDraftListingMutation` hook
- Provides optimistic updates and cache invalidation
- Handles success/error states and toast notifications

```typescript
export function useSaveDraftListingMutation(options: MutationOptions = {}) {
  // Implementation handles:
  // - Authentication token validation
  // - API call to draft endpoint
  // - Cache updates and invalidation
  // - Success/error handling
}
```

### 3. Form Component Updates

#### ListingForm.tsx
- Updated imports to include `useSaveDraftListingMutation` and `SaveDraftListingRequest`
- Added `saveDraftListingMutation` hook initialization
- Updated `handleSaveDraft` function to use the dedicated draft endpoint

#### ListingForm2.tsx
- Same updates as ListingForm.tsx for consistency

### 4. Key Improvements

#### Before (Old Implementation)
- Used regular create/update endpoints with `status: 'draft'`
- Required all mandatory fields to be present
- Could fail validation for incomplete data
- Different logic for editing vs creating drafts

#### After (New Implementation)
- Uses dedicated `/v1/listings/draft` endpoint
- Accepts incomplete data (all fields optional except status, listingType, teamVisibility)
- Simplified logic - no need to differentiate between editing and creating
- Better error handling for incomplete data

## API Schema

The `SaveDraftListingRequest` schema allows all fields to be optional except:
- `status`: Required, defaults to "draft"
- `listingType`: Required, defaults to "business_sale"  
- `teamVisibility`: Required, defaults to "all"

This enables saving listings with minimal data and progressively building them up.

## Usage Example

```typescript
// In a form component
const saveDraftMutation = useSaveDraftListingMutation();

const handleSaveDraft = async () => {
  const draftData = {
    businessName: 'Partial Business Name',
    status: 'draft',
    listingType: 'business_sale',
    teamVisibility: 'all',
    // Other fields can be omitted
  };
  
  await saveDraftMutation.mutateAsync(draftData);
};
```

## Benefits

1. **Better UX**: Users can save incomplete listings without validation errors
2. **Proper API Usage**: Uses the endpoint designed specifically for drafts
3. **Simplified Logic**: No need for complex editing vs creating logic
4. **Type Safety**: Full TypeScript support with proper types
5. **Consistent Behavior**: Same implementation across all form components

## Testing

The implementation has been tested by:
- Building the application successfully (no TypeScript errors)
- Running the development server without issues
- Verifying all imports and exports are correct
- Ensuring the API client methods are properly integrated

## Future Considerations

- Consider adding draft auto-save functionality
- Implement draft listing management (list, delete drafts)
- Add draft validation warnings in the UI
- Consider draft expiration policies
